import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

class AllSmartResolutionObjectMobile extends StatefulWidget {
  const AllSmartResolutionObjectMobile({super.key});

  @override
  State<AllSmartResolutionObjectMobile> createState() =>
      _AllSmartResolutionObjectMobileState();
}

class _AllSmartResolutionObjectMobileState
    extends State<AllSmartResolutionObjectMobile> {
  bool _isAIEnabled = true;
  // JSON data for the validation rules
  final Map<String, dynamic> validationData = {
    "header": {
      "title": "All Smart Resolution",
      "bulkApplyButton": "Bulk Apply"
    },
    "validationRules": [
      {
        "id": 1,
        "title": "Email Validation Rules",
        "description":
            "Add IS_VALID_EMAIL and IS_UNIQUE operators for email field",
        "status": "SIMPLE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 2,
        "title": "Phone Format Validation",
        "description":
            "Apply MATCHES_PATTERN operator for international phone numbers",
        "status": "MODERATE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 3,
        "title": "Customer-Address Relationship",
        "description":
            "Configure one-to-many with CASCADE delete for address cleanup",
        "status": "MODERATE",
        "actionType": "Attribute",
        "hasCircleIcon": false
      },
      {
        "id": 4,
        "title": "Title of the Issue",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      },
      {
        "id": 5,
        "title": "Address Auto-Complete",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      }
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: const CustomDrawer(),
      backgroundColor: const Color(0xFFF7F9FB),
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildValidationRulesList(),
                ),
              ],
            ),
            // Bottom action buttons positioned on top
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomActions(context),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFFF7F9FB),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0058FF), Color(0xFF0B3A91)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_fix_high,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            validationData['header']['title'],
            style: FontManager.getCustomStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              // Add bulk apply functionality here
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
              ),
              child: Text(
                validationData['header']['bulkApplyButton'],
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF0058FF),
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationRulesList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        itemCount: validationData['validationRules'].length,
        itemBuilder: (context, index) {
          final rule = validationData['validationRules'][index];
          return _buildValidationRuleItem(
            rule['title'],
            rule['description'],
            rule['actionType'],
            rule['status'],
          );
        },
      ),
    );
  }

  Widget _buildValidationRuleItem(
    String title,
    String description,
    String actionType,
    String status,
  ) {
    return GestureDetector(
      onTap: () {
        // Add validation rule item tap functionality here
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: const Color(0xFFE5E5E5),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              // Left blue accent bar
              Container(
                width: 4,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF0058FF), Color(0xFF0B3A91)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        title,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      const SizedBox(height: 6),
                      // Description
                      Text(
                        description,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF666666),
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // Action type and status row
                      Row(
                        children: [
                          Text(
                            actionType,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF666666),
                              fontFamily: FontManager.fontFamilyInter,
                            ),
                          ),
                          const Spacer(),
                          // Status badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getComplexityColor(status),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Left side icons in one container - stacked vertically
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Color(0xAAD0D0D0), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  offset: const Offset(0, 3), // X: 0, Y: 3
                  blurRadius: 20, // Blur: 20
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.menu,
                  size: 20,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(height: 8),
                Icon(
                  Icons.mic_none,
                  size: 20,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),

          const Spacer(),

          // Right side colored circles - stacked vertically
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  // Navigate back to ExtractDetailsObjectMobile or handle tap
                  Navigator.pop(context);
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF00BCD4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.black,
                        width: 2,
                      ),
                    ),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  color: Color(0xFF673AB7),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ], // Purple color
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
